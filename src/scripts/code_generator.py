# -*- coding: utf-8 -*-
"""
Script principal para geração de código Angular a partir de extrações do Figma.

Este script implementa o fluxo completo de 3 etapas:
1. Leitura do Figma (FigmaReader)
2. Mapeamento com Design System (DesignSystemMapper)
3. Geração final com IA (ComponentGenerator)

Interface simples e intuitiva para processar extrações do Figma.
"""

import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any

# Adicionar src ao path para imports
sys.path.append('.')
sys.path.append('src')

from src.utils.config import ConfigLoader
from src.utils.logging import setup_script_logging, get_logger
from src.utils.interactive_utils import interactive_mode, process_single_extraction
from src.generators.figma_reader import FigmaReader
from src.generators.design_system_mapper import DesignSystemMapper
from src.generators.component_generator import ComponentGenerator
from src.generators.wrapper_generator import WrapperGenerator

logger = get_logger(__name__)

class CodeGeneratorScript:
    """
    Script principal que orquestra a geração de código Angular.
    Interface simples e intuitiva para processar extrações do Figma.
    Fluxo de 3 etapas: Leitura → Mapeamento Design System → Geração com IA
    """
    
    def __init__(self, config_path: str = "project_config.yaml"):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.load_config()
        
        # Flow API será inicializado quando necessário
        self.flow_client = None
        
        # Inicializar geradores das 3 etapas (Flow API será passado depois)
        self.figma_reader = FigmaReader(config_path)
        self.ds_mapper = None  # Será inicializado quando necessário
        self.component_generator = None  # Será inicializado quando necessário
        self.wrapper_generator = None  # Será inicializado quando necessário
        
        # Configurações
        self.output_dir = Path(self.config.get("output", {}).get("generate_path", "data/output_generated"))
        self.figma_extraction_dir = Path(self.config.get("output", {}).get("figma_extraction_path", "data/figma_extraction"))
    
    def _initialize_shared_flow_api(self):
        """Inicializa Flow API uma vez para compartilhar entre classes."""
        try:
            from flow_api import FlowAPIClient
            client = FlowAPIClient()
            connection = client.check_connection()
            if connection['status'] == 'connected':
                logger.info("🤖 Flow API inicializada para busca e geração")
                return client
            else:
                logger.warning(f"⚠️ Flow API não disponível - status: {connection['status']}")
                return None
        except Exception as e:
            logger.warning(f"⚠️ Erro ao inicializar Flow API: {e}")
            return None
    
    def _initialize_generators(self):
        """Inicializa os geradores com Flow API compartilhado."""
        # Primeiro, inicializar o DesignSystemMapper para verificar se o design system está disponível
        if self.ds_mapper is None:
            self.ds_mapper = DesignSystemMapper(self.config_loader.config_file, flow_client=None)
        
        # Se chegou até aqui, o design system foi carregado com sucesso
        # Agora inicializar o Flow API
        if self.flow_client is None:
            self.flow_client = self._initialize_shared_flow_api()
        
        # Atualizar o flow_client no ds_mapper
        self.ds_mapper.flow_client = self.flow_client
        
        if self.component_generator is None:
            self.component_generator = ComponentGenerator(self.config_loader.config_file, flow_client=self.flow_client)
        
        if self.wrapper_generator is None:
            self.wrapper_generator = WrapperGenerator(self.config_loader.config_file, flow_client=self.flow_client)
    
    def _get_output_path(self, extraction_dir: Path = None, figma_data=None, folder_type: str = "figma_processed") -> Path:
        """
        Gera o path de output para uma extração.
        
        Args:
            extraction_dir: Diretório da extração do Figma (opcional)
            figma_data: Dados do Figma com component_name (opcional)
            folder_type: Tipo de pasta ("figma_processed" ou "angular")
            
        Returns:
            Path completo para salvar os arquivos
        """
        if extraction_dir:
            # Extrair nome do projeto e componente do path da extração
            project_name = extraction_dir.parts[-2] if len(extraction_dir.parts) >= 2 else "unknown_project"
            component_name = extraction_dir.parts[-1] if len(extraction_dir.parts) >= 1 else "unknown_component"
        elif figma_data and hasattr(figma_data, 'component_name'):
            # Usar o nome do componente do figma_data
            component_parts = figma_data.component_name.split('-')
            if len(component_parts) >= 2:
                project_name = component_parts[0]
                component_name = component_parts[1]
            else:
                project_name = "unknown_project"
                component_name = figma_data.component_name
        else:
            project_name = "unknown_project"
            component_name = "unknown_component"

        # Criar path: output_dir / project / component / folder_type
        output_path = self.output_dir / project_name / component_name / folder_type
        
        # Criar diretório se não existir
        output_path.mkdir(parents=True, exist_ok=True)
        
        return output_path
    
    def process_extraction(self, extraction_path: str, raw_only: bool = False) -> Dict[str, Any]:
        """
        Processa uma extração completa do Figma usando o fluxo de 3 etapas.
        
        Args:
            extraction_path: Caminho da extração do Figma a ser processada.
            raw_only: Se True, gera apenas o conteúdo de figma_processed
            
        Returns:
            Dict com resultados da geração para cada componente
        """
        extraction_dir = Path(extraction_path)
        
        if not extraction_dir.exists():
            raise FileNotFoundError(f"Extração não encontrada: {extraction_dir}")
        
        logger.debug(f"🚀 Processando extração: {extraction_dir}")
        
        # Inicializar geradores (incluindo Flow API) após confirmar que o design system está disponível
        self._initialize_generators()
        
        # Etapa 1: Leitura do Figma
        logger.info("\n\n📖 ETAPA 1: Leitura do Figma")
        all_figma_data = self._process_figma_reading(extraction_dir)
        
        # Etapa 2: Mapeamento com Design System para cada componente
        logger.info("\n\n🔗 ETAPA 2: Mapeamento com Design System")
        mapping_results = {}
        for figma_data in all_figma_data:
            mapping_result = self._process_design_system_mapping(figma_data, extraction_dir)
            mapping_results[figma_data.component_name] = mapping_result
        
        # Se raw_only, parar aqui
        if raw_only:
            logger.info("\n\n✅ Modo raw_only: Geração final pulada")
            return {
                'all_figma_data': all_figma_data,
                'mapping_results': mapping_results,
                'generation_results': {}
            }
        
        # Etapa 3: Geração Final com IA para cada componente
        logger.info("\n\n🚀 ETAPA 3: Geração Final com IA")
        generation_results = {}
        for figma_data in all_figma_data:
            mapping_result = mapping_results[figma_data.component_name]
            generation_result = self._process_component_generation(figma_data, mapping_result, extraction_dir)
            generation_results[figma_data.component_name] = generation_result
        
        # Etapa 4: Geração de wrappers via IA
        logger.info("\n\n🚀 ETAPA 4: Geração do Wrapper via IA")
        wrapper_result = self._process_wrapper_generation(extraction_dir)
        
        return {
            'all_figma_data': all_figma_data,
            'mapping_results': mapping_results,
            'generation_results': generation_results,
            'wrapper_generation': wrapper_result
        }
    
    def _process_figma_reading(self, extraction_dir: Path):
        """Etapa 1: Leitura do Figma."""
        all_figma_data = []
        all_webcomponents = []
        
        # Procurar arquivo principal (container)
        main_files = list(extraction_dir.glob("*.json"))
        if not main_files:
            raise ValueError(f"Arquivo principal não encontrado em {extraction_dir}")
        
        # Processar arquivo principal
        main_file = main_files[0]
        logger.debug(f"📄 Carregando Figma principal: {main_file}")
        
        # Ler dados do Figma principal
        main_figma_data = self.figma_reader.read_figma_component(str(main_file))
        all_figma_data.append(main_figma_data)
        
        # Coletar webcomponents do componente principal
        all_webcomponents.extend(main_figma_data.webcomponents)
        
        # Salvar HTML do arquivo principal
        output_path = self._get_output_path(extraction_dir, folder_type="figma_processed")
        self.figma_reader.save_raw_html(main_figma_data, str(output_path / f"{main_figma_data.normalized_name}-raw.component.html"))
        
        # Procurar pasta "components"
        components_dir = extraction_dir / "components"
        if components_dir.exists() and components_dir.is_dir():
            logger.debug(f"📁 Encontrada pasta components: {components_dir}")
            
            # Buscar todos os arquivos JSON na pasta components
            component_files = list(components_dir.glob("*.json"))
            
            if component_files:
                logger.info(f"📦 Processando {len(component_files)} componentes adicionais...")
                
                for component_file in component_files:
                    try:
                        logger.debug(f"📄 Carregando componente: {component_file}")
                        
                        # Ler dados do componente
                        component_data = self.figma_reader.read_figma_component(str(component_file))
                        all_figma_data.append(component_data)
                        
                        # Coletar webcomponents do componente filho
                        all_webcomponents.extend(component_data.webcomponents)
                        
                        # Salvar HTML do componente
                        self.figma_reader.save_raw_html(component_data, str(output_path / f"{component_data.normalized_name}-raw.component.html"))
                        
                        logger.debug(f"✅ Componente processado: {component_data.component_name}")
                        
                    except Exception as e:
                        logger.error(f"❌ Erro ao processar componente {component_file}: {e}")
                        continue
            else:
                logger.debug("📁 Pasta components encontrada, mas sem arquivos JSON")
        else:
            logger.debug("📁 Pasta components não encontrada")
        
        logger.info(f"✅ Leitura concluída: {len(all_figma_data)} componente(s) processado(s)")
        logger.info(f"🔍 Total de webcomponents encontrados: {len(all_webcomponents)}")
        
        # Atualizar todos os componentes com todos os webcomponents encontrados
        for figma_data in all_figma_data:
            figma_data.webcomponents = all_webcomponents
            # Adicionar apenas informações básicas dos componentes, não os objetos completos
            figma_data.metadata['all_components_count'] = len(all_figma_data)
            figma_data.metadata['all_components_names'] = [comp.component_name for comp in all_figma_data]
            figma_data.metadata['total_webcomponents'] = len(all_webcomponents)
        
        return all_figma_data
    
    def _process_design_system_mapping(self, figma_data, extraction_dir: Path):
        """Etapa 2: Mapeamento com Design System."""
        # Mapear webcomponents com Design System
        mapping_result = self.ds_mapper.map_figma_to_design_system(figma_data)
        
        # Salvar resultado do mapeamento
        output_path = self._get_output_path(extraction_dir=extraction_dir, folder_type="figma_processed")
        self.ds_mapper.save_mapping_result(mapping_result, str(output_path / f"{figma_data.normalized_name}-design-system-mapping.json"))
        
        logger.info(f"✅ Mapeamento concluído: {mapping_result.metadata['mapped_components']}/{mapping_result.metadata['total_webcomponents']} componentes mapeados")
        return mapping_result
    
    def _process_component_generation(self, figma_data, mapping_result, extraction_dir: Path):
        """Etapa 3: Geração final com IA."""
        # Gerar código final (HTML, TS, SCSS)
        output_path = self._get_output_path(extraction_dir=extraction_dir, folder_type="angular")
        self.component_generator.generate_component_code(figma_data, mapping_result, str(output_path))
        
        logger.info(f"✅ Geração final concluída: {figma_data.component_name}")
        return {"success": True, "output_path": str(output_path)}
    
    def _process_wrapper_generation(self, extraction_dir: Path):
        """Etapa 4: Geração de wrappers via IA (após geração dos componentes individuais)."""
        # Gerar wrappers a partir dos metadados salvos
        output_path = self._get_output_path(extraction_dir=extraction_dir, folder_type="angular")
        self.wrapper_generator.generate_wrappers_from_metadata(str(output_path))
        
        # Reorganizar estrutura para melhor integração com Angular
        self._reorganize_angular_structure(output_path)
        
        logger.info("✅ Geração de wrappers concluída!")
        return {"success": True, "output_path": str(output_path)}
    
    def _reorganize_angular_structure(self, angular_path: Path):
        """
        Reorganiza a estrutura Angular para facilitar integração com projetos existentes.
        
        Estrutura final:
        angular/
        └── [component_name]/
            ├── [component_name].component.html
            ├── [component_name].component.ts
            ├── [component_name].component.scss
            └── components/
                ├── [child1]/
                ├── [child2]/
                └── ...
        """
        logger.info("🔄 Reorganizando estrutura Angular...")
        
        # Encontrar o wrapper (pasta com mesmo nome do componente principal)
        wrapper_dir = None
        child_components = []
        
        for item in angular_path.iterdir():
            if item.is_dir() and not item.name.endswith('_metadata'):
                # Verificar se é o wrapper (tem arquivos .component.*)
                if any(item.glob("*.component.*")):
                    if wrapper_dir is None:
                        wrapper_dir = item
                    else:
                        child_components.append(item)
                else:
                    child_components.append(item)
        
        if not wrapper_dir:
            logger.warning("⚠️ Wrapper não encontrado, mantendo estrutura atual")
            return
        
        # Criar pasta components dentro do wrapper
        components_dir = wrapper_dir / "components"
        components_dir.mkdir(exist_ok=True)
        
        # Mover componentes filhos para pasta components
        for child_dir in child_components:
            if child_dir != wrapper_dir:
                # Mover pasta do componente filho para components/
                new_path = components_dir / child_dir.name
                if new_path.exists():
                    # Se já existe, remover primeiro
                    import shutil
                    shutil.rmtree(new_path)
                
                # Mover pasta
                child_dir.rename(new_path)
                logger.debug(f"📦 Movido {child_dir.name} para components/")
        
        # Atualizar imports no wrapper TypeScript
        self._update_wrapper_imports(wrapper_dir, components_dir)
        
        logger.info(f"✅ Estrutura reorganizada: {wrapper_dir.name}/components/")
    
    def _update_wrapper_imports(self, wrapper_dir: Path, components_dir: Path):
        """Atualiza imports no arquivo TypeScript do wrapper."""
        ts_file = next(wrapper_dir.glob("*.component.ts"), None)
        if not ts_file:
            return
        
        # Ler conteúdo atual
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Atualizar imports para usar caminho relativo components/
        updated_content = content
        for child_dir in components_dir.iterdir():
            if child_dir.is_dir():
                child_name = child_dir.name
                old_import = f"import {{ {self._to_pascal_case(child_name)}Component }} from '../{child_name}/{child_name}.component';"
                new_import = f"import {{ {self._to_pascal_case(child_name)}Component }} from './components/{child_name}/{child_name}.component';"
                updated_content = updated_content.replace(old_import, new_import)
        
        # Salvar arquivo atualizado
        with open(ts_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.debug(f"📝 Imports atualizados em {ts_file.name}")
    
    def _to_pascal_case(self, name: str) -> str:
        """Converte nome para PascalCase."""
        return ''.join(word.capitalize() for word in name.replace('-', ' ').replace('_', ' ').split())
    
    def list_available_extractions(self) -> List[str]:
        """Lista extrações disponíveis."""
        extractions = []
        
        if self.figma_extraction_dir.exists():
            for project_dir in self.figma_extraction_dir.iterdir():
                if project_dir.is_dir():
                    for extraction_dir in project_dir.iterdir():
                        if extraction_dir.is_dir() and any(extraction_dir.glob("*.json")):
                            extractions.append(str(extraction_dir))
        
        return extractions
    
    def _process_wrappers_only(self, output_dir: str):
        """Processa apenas a geração de wrappers a partir de metadados existentes."""
        from pathlib import Path
        
        logger.info("🚀 Iniciando geração de wrappers via IA...")
        
        # Inicializar geradores (incluindo Flow API)
        self._initialize_generators()
        
        # Verificar se o diretório existe
        output_path = Path(output_dir)
        if not output_path.exists():
            logger.error(f"❌ Diretório não encontrado: {output_path}")
            return
        
        try:
            # Gerar wrappers a partir dos metadados salvos
            self.wrapper_generator.generate_wrappers_from_metadata(str(output_path))
            
            logger.info("✅ Geração de wrappers concluída!")
            
        except Exception as e:
            error_message = str(e)
            if "Índice do Design System não encontrado" in error_message:
                sys.exit(1)  # Encerrar aplicação com código de erro
            else:
                logger.error(f"❌ Erro durante a geração de wrappers: {e}")
                raise



def main():
    """Função principal do script."""
    parser = argparse.ArgumentParser(
        description="Gerador de código Angular baseado em dados do Figma",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=""
    )

    parser.add_argument(
        "--extraction", "-e",
        help="Caminho para a extração do Figma a ser processada"
    )
    
    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="Lista extrações disponíveis"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Modo interativo"
    )
    
    parser.add_argument(
        "--raw_only",
        action="store_true",
        help="Gera apenas o conteúdo de figma_processed (pula geração Angular)"
    )
    
    parser.add_argument(
        "--wrappers_only",
        action="store_true",
        help="Gera apenas wrappers a partir de metadados existentes"
    )
    
    parser.add_argument(
        "--output_dir",
        type=str,
        default='data/output_generated',
        help='Diretório onde procurar metadados de wrapper (para --wrappers_only)'
    )
    
    args = parser.parse_args()
    
    # Configurar logging
    setup_script_logging("code_generator", verbose=True)
    
    try:
        generator = CodeGeneratorScript()
        
        if args.list:
            # Listar extrações
            extractions = generator.list_available_extractions()
            if extractions:
                print("📋 EXTRAÇÕES DISPONÍVEIS:")
                for extraction in extractions:
                    print(f"  📁 {extraction}")
            else:
                print("❌ Nenhuma extração encontrada!")
        
        elif args.interactive:
            # Modo interativo
            interactive_mode(generator)
        
        elif args.wrappers_only:
            # Gerar apenas wrappers
            _process_wrappers_only(generator, args.output_dir)
        
        elif args.extraction:
            # Processar extração específica
            process_single_extraction(generator, args.extraction, raw_only=args.raw_only)
        
        else:
            # Modo interativo por padrão
            interactive_mode(generator)
    
    except Exception as e:
        error_message = str(e)
        if "Índice do Design System não encontrado" in error_message:
            sys.exit(1)  # Encerrar aplicação com código de erro
        else:
            logger.error(f"❌ Erro no script principal: {e}")
            print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main() 