# -*- coding: utf-8 -*-
"""
Figma Data Processor - Processamento de dados do Figma com IA.

Este módulo é responsável por extrair e estruturar dados do Figma usando IA
para melhorar a qualidade da geração de código.
"""

import json
import base64
import re
from pathlib import Path
from typing import Dict, Any, List, Optional

from src.utils.config import ConfigLoader
from src.utils.logging import get_logger
from src.generators.figma_reader import FigmaComponentData

logger = get_logger(__name__)

class FigmaDataProcessor:
    """
    Processa dados do Figma usando IA para extração estruturada.
    """
    
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        self.config_loader = ConfigLoader(config_path)
        self.flow_client = flow_client
        self.prompts = self._load_prompts()
    
    def _load_prompts(self) -> Dict[str, Any]:
        """Carrega prompts do arquivo YAML."""
        import yaml
        prompts_path = Path(__file__).parent.parent / "prompts.yaml"
        with open(prompts_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def preprocess_with_ai(self, figma_data: FigmaComponentData) -> Dict[str, Any]:
        """
        Usa IA para pré-processar e estruturar dados do Figma.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dict com dados estruturados processados pela IA
        """
        if not self.flow_client:
            logger.warning("⚠️ Flow API não disponível, usando extração manual")
            return self._extract_structured_data_manual(figma_data)
        
        try:
            # Preparar contexto para IA
            context_data = self._prepare_ai_context(figma_data)
            
            # Buscar imagem do Figma
            figma_image = self._get_figma_image(figma_data)
            if figma_image:
                context_data["figma_image"] = figma_image
                logger.debug("📸 Imagem do Figma incluída no contexto")
            
            # Processar com IA
            ai_processed_data = self._process_with_ai(context_data)
             
            return ai_processed_data
            
        except Exception as e:
            logger.warning(f"⚠️ Erro no pré-processamento com IA: {e}")
            return self._extract_structured_data_manual(figma_data)
    
    def _prepare_ai_context(self, figma_data: FigmaComponentData) -> Dict[str, Any]:
        """Prepara contexto para envio à IA."""
        # Converter FigmaComponentData para dict serializável
        metadata_dict = {}
        if figma_data.metadata:
            logger.debug(f"🔍 Processando metadata com {len(figma_data.metadata)} chaves")
            for key, value in figma_data.metadata.items():
                try:
                    if isinstance(value, (dict, list, str, int, float, bool)) or value is None:
                        metadata_dict[key] = value
                    elif hasattr(value, '__dict__'):
                        # Se for um objeto, tentar extrair atributos básicos
                        try:
                            obj_dict = {}
                            for attr in dir(value):
                                if not attr.startswith('_'):
                                    try:
                                        attr_value = getattr(value, attr)
                                        if isinstance(attr_value, (dict, list, str, int, float, bool)) or attr_value is None:
                                            obj_dict[attr] = attr_value
                                        else:
                                            obj_dict[attr] = str(attr_value)
                                    except Exception:
                                        obj_dict[attr] = str(attr_value)
                            metadata_dict[key] = obj_dict
                        except Exception:
                            metadata_dict[key] = str(value)
                    else:
                        metadata_dict[key] = str(value)
                except Exception as e:
                    logger.warning(f"⚠️ Erro ao serializar metadata[{key}]: {e}")
                    logger.debug(f"   Valor problemático: {type(value)} - {str(value)[:100]}...")
                    logger.debug(f"   Chave: {key}")
                    metadata_dict[key] = str(value)
        
        webcomponents_list = []
        if figma_data.webcomponents:
            for wc in figma_data.webcomponents:
                try:
                    # Se webcomponent já for dict, usar diretamente
                    if isinstance(wc, dict):
                        webcomponents_list.append(wc)
                    else:
                        # Tentar converter webcomponent para dict
                        wc_dict = {}
                        for attr in dir(wc):
                            if not attr.startswith('_'):
                                try:
                                    value = getattr(wc, attr)
                                    if isinstance(value, (dict, list, str, int, float, bool)) or value is None:
                                        wc_dict[attr] = value
                                    else:
                                        wc_dict[attr] = str(value)
                                except Exception:
                                    wc_dict[attr] = str(value)
                        webcomponents_list.append(wc_dict)
                except Exception as e:
                    # Se falhar, criar dict básico
                    logger.warning(f"⚠️ Erro ao converter webcomponent: {e}")
                    webcomponents_list.append({
                        "id": str(getattr(wc, 'id', 'unknown')),
                        "name": str(getattr(wc, 'name', 'unknown')),
                        "type": str(getattr(wc, 'type', 'unknown'))
                    })
        
        # Verificar se o resultado é serializável antes de retornar
        try:
            test_json = json.dumps({
                "component_name": figma_data.component_name,
                "figma_id": figma_data.figma_id,
                "raw_html": figma_data.html_structure,
                "metadata": metadata_dict,
                "webcomponents": webcomponents_list,
                "css_styles": figma_data.css_styles
            })
            logger.debug(f"✅ Contexto serializado com sucesso: {len(test_json)} caracteres")
            logger.info(f"✅ Serialização do metadata corrigida para: {figma_data.component_name}")
        except Exception as e:
            logger.error(f"❌ Erro ao testar serialização do contexto: {e}")
            # Se falhar, criar contexto básico
            return {
                "component_name": figma_data.component_name,
                "figma_id": figma_data.figma_id,
                "raw_html": figma_data.html_structure,
                "metadata": {"error": "serialization_failed"},
                "webcomponents": [],
                "css_styles": {}
            }
        
        return {
            "component_name": figma_data.component_name,
            "figma_id": figma_data.figma_id,
            "raw_html": figma_data.html_structure,
            "metadata": metadata_dict,
            "webcomponents": webcomponents_list,
            "css_styles": figma_data.css_styles
        }
    
    def _get_figma_image(self, figma_data: FigmaComponentData) -> Optional[str]:
        """
        Busca imagem PNG do Figma na pasta de extração.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            String base64 da imagem ou None se não encontrada
        """
        try:
            # Tentar encontrar imagem na pasta de extração
            extraction_path = self._find_extraction_path(figma_data)
            if not extraction_path:
                return None
            
            # Buscar por arquivo PNG
            png_files = list(extraction_path.glob("*.png"))
            if not png_files:
                return None
            
            # Usar o primeiro PNG encontrado
            image_path = png_files[0]
            logger.debug(f"📸 Encontrada imagem: {image_path}")
            
            # Codificar em base64
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
                
        except Exception as e:
            logger.warning(f"⚠️ Erro ao buscar imagem do Figma: {e}")
            return None
    
    def _find_extraction_path(self, figma_data: FigmaComponentData) -> Optional[Path]:
        """Encontra o path da extração do Figma."""
        try:
            # Buscar na pasta figma_extraction
            figma_extraction_dir = Path("data/figma_extraction")
            if not figma_extraction_dir.exists():
                return None
            
            # Buscar por pasta que contenha o figma_id
            for project_dir in figma_extraction_dir.iterdir():
                if project_dir.is_dir():
                    for extraction_dir in project_dir.iterdir():
                        if extraction_dir.is_dir():
                            # Verificar se há arquivo JSON com o figma_id
                            json_files = list(extraction_dir.glob("*.json"))
                            for json_file in json_files:
                                try:
                                    with open(json_file, 'r', encoding='utf-8') as f:
                                        json_data = json.load(f)
                                        if json_data.get('id') == figma_data.figma_id:
                                            return extraction_dir
                                except:
                                    continue
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ Erro ao buscar path de extração: {e}")
            return None
    
    def _process_with_ai(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processa dados com IA usando prompts do arquivo YAML.
        
        Args:
            context_data: Dados do contexto para IA
            
        Returns:
            Dict com dados estruturados processados pela IA
        """
        try:
            # Usar prompts do arquivo YAML
            system_prompt = self.prompts['system_prompts']['component_analysis']['role'] + "\n" + \
                           self.prompts['system_prompts']['component_analysis']['mission'] + "\n" + \
                           self.prompts['system_prompts']['component_analysis']['guidelines']
            
            user_prompt = self.prompts['user_prompts']['component_analysis']['task']
            user_prompt += f"\n\nCOMPONENTE: {context_data['component_name']}"
            user_prompt += f"\nFIGMA ID: {context_data['figma_id']}"
            
            # Adicionar orientações sobre a imagem se disponível
            if context_data.get('figma_image'):
                user_prompt += "\n\n📸 IMAGEM DA TELA COMPLETA DISPONÍVEL:"
                user_prompt += "\n- Analise a imagem da tela completa para entender o contexto visual"
                user_prompt += "\n- Identifique a posição e relacionamento do componente na tela"
                user_prompt += "\n- Use a imagem para melhorar a análise da estrutura e interações"
                user_prompt += f"\n- Imagem em base64: {context_data['figma_image'][:100]}..."  # Primeiros 100 chars para referência
                logger.debug("📸 Imagem incluída no prompt para IA")
            
            user_prompt += "\n\nDADOS DO FIGMA:"
            
            # Testar serialização dos dados antes de enviar
            try:
                metadata_json = json.dumps(context_data['metadata'], ensure_ascii=False, indent=2)
                user_prompt += f"\n{metadata_json}"
            except Exception as e:
                logger.warning(f"⚠️ Erro ao serializar metadata: {e}")
                user_prompt += "\n{'error': 'metadata serialization failed'}"
            
            user_prompt += "\n\nHTML RAW:"
            user_prompt += f"\n{context_data['raw_html']}"
            user_prompt += "\n\nWEBCOMPONENTS:"
            
            try:
                webcomponents_json = json.dumps(context_data['webcomponents'], ensure_ascii=False, indent=2)
                user_prompt += f"\n{webcomponents_json}"
            except Exception as e:
                logger.warning(f"⚠️ Erro ao serializar webcomponents: {e}")
                user_prompt += "\n{'error': 'webcomponents serialization failed'}"
            
            # Usar modelo de análise conforme configuração existente
            model = self.config_loader.get_ai_config()['model']['analysis']
            temperature = self.config_loader.get_ai_config().get('temperature')
            max_tokens = self.config_loader.get_ai_config().get('max_tokens')

            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Verificar se a resposta não está vazia
            if not response or response.strip() == "":
                logger.warning("⚠️ Resposta da IA vazia, usando extração manual")
                return self._extract_structured_data_manual(context_data)
            
            # Limpar resposta da IA (remover espaços extras, quebras de linha)
            response = response.strip()
            
            # Log da resposta para debug
            logger.debug(f"📝 Resposta da IA completa: {len(response)} caracteres")
            logger.debug(f"📝 Primeiros 500 chars: {response[:500]}...")
            logger.debug(f"📝 Últimos 500 chars: {response[-500:] if len(response) > 500 else response}")
            
            # Verificar se a resposta está em formato markdown primeiro
            if response.startswith('```'):
                logger.debug("📝 Resposta detectada como markdown, extraindo JSON...")
                try:
                    # Procurar por blocos de código JSON
                    json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                    json_matches = re.findall(json_pattern, response, re.DOTALL)
                    
                    if json_matches:
                        # Tentar o primeiro match
                        json_str = json_matches[0].strip()
                        logger.debug(f"📝 JSON extraído de markdown: {len(json_str)} caracteres")
                        logger.debug(f"📝 JSON extraído: {json_str[:200]}...")
                        ai_processed_data = json.loads(json_str)
                        logger.debug(f"✅ JSON extraído de markdown: {len(ai_processed_data)} seções")
                        return ai_processed_data
                    else:
                        logger.warning("⚠️ Markdown detectado mas nenhum JSON encontrado")
                        return self._extract_structured_data_manual(context_data)
                except Exception as extract_error:
                    logger.warning(f"⚠️ Falha ao extrair JSON de markdown: {extract_error}")
                    return self._extract_structured_data_manual(context_data)
            
            # Parse da resposta da IA (tentativa de JSON direto)
            try:
                # Tentar fazer parse direto
                ai_processed_data = json.loads(response)
                logger.debug(f"✅ Dados processados pela IA: {len(ai_processed_data)} seções")
                return ai_processed_data
            except json.JSONDecodeError as e:
                logger.warning(f"⚠️ Erro ao fazer parse da resposta da IA: {e}")
                logger.debug(f"📝 Resposta da IA: {response[:200]}...")
                
                # Tentar extrair JSON da resposta se estiver dentro de markdown
                try:
                    # Procurar por blocos de código JSON
                    json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                    json_matches = re.findall(json_pattern, response, re.DOTALL)
                    
                    if json_matches:
                        # Tentar o primeiro match
                        json_str = json_matches[0].strip()
                        logger.debug(f"📝 JSON extraído de markdown: {len(json_str)} caracteres")
                        logger.debug(f"📝 JSON extraído: {json_str[:200]}...")
                        ai_processed_data = json.loads(json_str)
                        logger.debug(f"✅ JSON extraído de markdown: {len(ai_processed_data)} seções")
                        return ai_processed_data
                except Exception as extract_error:
                    logger.warning(f"⚠️ Falha ao extrair JSON de markdown: {extract_error}")
                
                # Se tudo falhar, usar extração manual
                logger.warning("⚠️ Usando extração manual como fallback")
                return self._extract_structured_data_manual(context_data)
            
        except Exception as e:
            logger.error(f"❌ Erro no processamento com IA: {e}")
            return self._extract_structured_data_manual(context_data)
    
    def _extract_structured_data_manual(self, figma_data) -> Dict[str, Any]:
        """
        Extração manual de dados estruturados (fallback).
        
        Args:
            figma_data: Dados do Figma (FigmaComponentData ou Dict)
            
        Returns:
            Dict com dados estruturados extraídos manualmente
        """
        # Se for dict (context_data), extrair dados básicos
        if isinstance(figma_data, dict):
            return {
                "text_elements": [],
                "interactive_elements": [],
                "layout_structure": {
                    "layout_type": "component",
                    "sections": [],
                    "spacing": "default"
                },
                "component_properties": {},
                "callbacks": []
            }
        
        # Se for FigmaComponentData, fazer extração completa
        return {
            "text_elements": self._extract_text_elements(figma_data),
            "interactive_elements": self._extract_interactive_elements(figma_data),
            "layout_structure": self._extract_layout_structure(figma_data),
            "component_properties": figma_data.metadata.get('props', {}),
            "callbacks": []
        }
    
    def _extract_text_elements(self, figma_data: FigmaComponentData) -> List[Dict]:
        """Extrai elementos de texto do Figma."""
        text_elements = []
        
        try:
            # Extrair textos do HTML raw
            text_pattern = r'<div[^>]*class="[^"]*figma-text[^"]*"[^>]*>([^<]+)</div>'
            matches = re.findall(text_pattern, figma_data.html_structure)
            
            for i, text in enumerate(matches):
                text_elements.append({
                    "id": f"text_{i}",
                    "content": text.strip(),
                    "type": "text"
                })
            
            logger.debug(f"📝 Extraídos {len(text_elements)} elementos de texto")
            
        except Exception as e:
            logger.warning(f"⚠️ Erro ao extrair textos: {e}")
        
        return text_elements
    
    def _extract_interactive_elements(self, figma_data: FigmaComponentData) -> List[Dict]:
        """Extrai elementos interativos do Figma."""
        interactive_elements = []
        
        try:
            # Extrair botões e elementos interativos
            button_pattern = r'<div[^>]*class="[^"]*figma-instance[^"]*"[^>]*data-component-id="([^"]*)"[^>]*>'
            matches = re.findall(button_pattern, figma_data.html_structure)
            
            for i, component_id in enumerate(matches):
                interactive_elements.append({
                    "id": f"interactive_{i}",
                    "component_id": component_id,
                    "type": "button" if "button" in component_id.lower() else "interactive"
                })
            
            logger.debug(f"🔘 Extraídos {len(interactive_elements)} elementos interativos")
            
        except Exception as e:
            logger.warning(f"⚠️ Erro ao extrair elementos interativos: {e}")
        
        return interactive_elements
    
    def _extract_layout_structure(self, figma_data: FigmaComponentData) -> Dict:
        """Extrai informações de layout e estrutura."""
        return {
            "layout_type": self._detect_layout_type(figma_data),
            "sections": self._extract_sections(figma_data),
            "spacing": "default"
        }
    
    def _detect_layout_type(self, figma_data: FigmaComponentData) -> str:
        """Detecta o tipo de layout."""
        name = figma_data.component_name.lower()
        
        if 'table' in name:
            return 'table'
        elif 'form' in name:
            return 'form'
        elif 'modal' in name or 'dialog' in name:
            return 'modal'
        else:
            return 'component'
    
    def _extract_sections(self, figma_data: FigmaComponentData) -> List[Dict]:
        """Extrai seções do componente."""
        sections = []
        
        try:
            # Extrair seções baseadas na estrutura HTML
            section_pattern = r'<div[^>]*data-figma-name="([^"]*)"[^>]*>'
            matches = re.findall(section_pattern, figma_data.html_structure)
            
            for section_name in matches:
                if section_name and not section_name.startswith('Frame'):
                    sections.append({
                        "name": section_name,
                        "type": "section"
                    })
            
        except Exception as e:
            logger.warning(f"⚠️ Erro ao extrair seções: {e}")
        
        return sections
