"""
Sistema de mapeamento de cores do Figma para classes CSS do Design System.

Este módulo implementa:
- Conversão de cores RGB/HEX do Figma para classes CSS
- Algoritmo de similaridade de cores
- Mapeamento para classes de cores do Design System
- Preservação de fidelidade visual
"""

import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from colorsys import rgb_to_hsv
import math

logger = logging.getLogger(__name__)


@dataclass
class ColorInfo:
    """Informações de uma cor do Figma."""
    r: int
    g: int
    b: int
    a: float = 1.0
    hex: str = ""
    rgb: str = ""
    
    def __post_init__(self):
        if not self.hex:
            self.hex = f"#{self.r:02x}{self.g:02x}{self.b:02x}"
        if not self.rgb:
            self.rgb = f"rgb({self.r}, {self.g}, {self.b})"


@dataclass
class DesignSystemColor:
    """Cor do Design System."""
    name: str
    class_name: str
    hex: str
    rgb: str
    theme: str = ""
    category: str = ""


class ColorMapper:
    """
    Mapeador de cores do Figma para classes CSS do Design System.
    
    Implementa:
    - Conversão de cores RGB/HEX
    - Algoritmo de similaridade de cores
    - Mapeamento para classes do Design System
    - Preservação de fidelidade visual
    """
    
    def __init__(self, design_system_colors: List[DesignSystemColor] = None):
        self.design_system_colors = design_system_colors or []
        self._build_color_index()
    
    def _build_color_index(self):
        """Constrói índice de cores do Design System para busca rápida."""
        self.color_index = {}
        
        for color in self.design_system_colors:
            # Indexar por nome
            self.color_index[color.name.lower()] = color
            
            # Indexar por classe
            self.color_index[color.class_name] = color
            
            # Indexar por hex
            self.color_index[color.hex.lower()] = color
            
            # Indexar por rgb
            self.color_index[color.rgb.lower()] = color
    
    def parse_figma_color(self, color_data: Dict[str, Any]) -> Optional[ColorInfo]:
        """
        Converte dados de cor do Figma para ColorInfo.
        
        Args:
            color_data: Dados de cor do Figma (r, g, b, a)
            
        Returns:
            ColorInfo ou None se inválido
        """
        try:
            if not color_data:
                return None
            
            # Extrair valores RGB (0-1 do Figma)
            r = int(color_data.get('r', 0) * 255)
            g = int(color_data.get('g', 0) * 255)
            b = int(color_data.get('b', 0) * 255)
            a = color_data.get('a', 1.0)
            
            return ColorInfo(r=r, g=g, b=b, a=a)
            
        except Exception as e:
            logger.warning(f"Erro ao processar cor do Figma: {e}")
            return None
    
    def find_best_color_match(self, figma_color: ColorInfo, 
                            tolerance: float = 0.1) -> Tuple[Optional[DesignSystemColor], float]:
        """
        Encontra a melhor correspondência de cor no Design System.
        
        Args:
            figma_color: Cor do Figma
            tolerance: Tolerância para similaridade (0-1)
            
        Returns:
            Tupla (DesignSystemColor, similaridade)
        """
        best_match = None
        best_similarity = 0.0
        
        for ds_color in self.design_system_colors:
            similarity = self._calculate_color_similarity(figma_color, ds_color)
            
            if similarity > best_similarity and similarity >= tolerance:
                best_similarity = similarity
                best_match = ds_color
        
        return best_match, best_similarity
    
    def _calculate_color_similarity(self, figma_color: ColorInfo, 
                                  ds_color: DesignSystemColor) -> float:
        """
        Calcula similaridade entre duas cores usando múltiplos algoritmos.
        
        Args:
            figma_color: Cor do Figma
            ds_color: Cor do Design System
            
        Returns:
            Similaridade (0-1)
        """
        # Converter hex do Design System para RGB
        ds_rgb = self._hex_to_rgb(ds_color.hex)
        if not ds_rgb:
            return 0.0
        
        ds_r, ds_g, ds_b = ds_rgb
        
        # Calcular similaridade usando múltiplos métodos
        similarities = []
        
        # 1. Distância euclidiana RGB
        rgb_distance = math.sqrt(
            (figma_color.r - ds_r) ** 2 +
            (figma_color.g - ds_g) ** 2 +
            (figma_color.b - ds_b) ** 2
        )
        rgb_similarity = 1 - (rgb_distance / 441.67)  # 441.67 = sqrt(255² + 255² + 255²)
        similarities.append(rgb_similarity)
        
        # 2. Similaridade HSV
        hsv_similarity = self._calculate_hsv_similarity(figma_color, ds_color)
        similarities.append(hsv_similarity)
        
        # 3. Similaridade de luminância
        luminance_similarity = self._calculate_luminance_similarity(figma_color, ds_color)
        similarities.append(luminance_similarity)
        
        # Média ponderada das similaridades
        return sum(similarities) / len(similarities)
    
    def _hex_to_rgb(self, hex_color: str) -> Optional[Tuple[int, int, int]]:
        """Converte cor HEX para RGB."""
        try:
            hex_color = hex_color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return r, g, b
        except ValueError:
            pass
        return None
    
    def _calculate_hsv_similarity(self, figma_color: ColorInfo, 
                                 ds_color: DesignSystemColor) -> float:
        """Calcula similaridade baseada em HSV."""
        try:
            # Converter RGB para HSV
            figma_hsv = rgb_to_hsv(figma_color.r/255, figma_color.g/255, figma_color.b/255)
            
            ds_rgb = self._hex_to_rgb(ds_color.hex)
            if not ds_rgb:
                return 0.0
            
            ds_hsv = rgb_to_hsv(ds_rgb[0]/255, ds_rgb[1]/255, ds_rgb[2]/255)
            
            # Calcular diferença HSV
            h_diff = min(abs(figma_hsv[0] - ds_hsv[0]), 1 - abs(figma_hsv[0] - ds_hsv[0]))
            s_diff = abs(figma_hsv[1] - ds_hsv[1])
            v_diff = abs(figma_hsv[2] - ds_hsv[2])
            
            # Similaridade HSV (pesos: H=0.5, S=0.3, V=0.2)
            hsv_similarity = 1 - (0.5 * h_diff + 0.3 * s_diff + 0.2 * v_diff)
            
            return max(0, hsv_similarity)
            
        except Exception:
            return 0.0
    
    def _calculate_luminance_similarity(self, figma_color: ColorInfo, 
                                       ds_color: DesignSystemColor) -> float:
        """Calcula similaridade baseada em luminância."""
        try:
            # Fórmula de luminância relativa
            def luminance(r, g, b):
                return 0.299 * r + 0.587 * g + 0.114 * b
            
            figma_lum = luminance(figma_color.r, figma_color.g, figma_color.b)
            
            ds_rgb = self._hex_to_rgb(ds_color.hex)
            if not ds_rgb:
                return 0.0
            
            ds_lum = luminance(ds_rgb[0], ds_rgb[1], ds_rgb[2])
            
            # Similaridade de luminância
            lum_diff = abs(figma_lum - ds_lum) / 255
            return 1 - lum_diff
            
        except Exception:
            return 0.0
    
    def map_figma_colors_to_css_classes(self, figma_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Mapeia cores do Figma para classes CSS do Design System.
        
        Args:
            figma_data: Dados do Figma com informações de cor
            
        Returns:
            Dict com mapeamento de cores para classes CSS
        """
        color_mapping = {}
        
        # Extrair cores do Figma
        figma_colors = self._extract_figma_colors(figma_data)
        
        for color_id, figma_color in figma_colors.items():
            # Encontrar melhor correspondência
            best_match, similarity = self.find_best_color_match(figma_color)
            
            if best_match and similarity >= 0.7:  # Threshold de 70%
                color_mapping[color_id] = best_match.class_name
                logger.debug(f"🎨 {color_id}: {figma_color.hex} → {best_match.class_name} (similaridade: {similarity:.2f})")
            else:
                # Fallback: usar cor inline se não encontrar correspondência
                color_mapping[color_id] = f"color: {figma_color.rgb}"
                logger.debug(f"🎨 {color_id}: {figma_color.hex} → cor inline (melhor similaridade: {similarity:.2f})")
        
        return color_mapping
    
    def _extract_figma_colors(self, figma_data: Dict[str, Any]) -> Dict[str, ColorInfo]:
        """Extrai todas as cores do Figma."""
        colors = {}
        
        def extract_colors_recursive(node: Dict[str, Any], prefix: str = ""):
            node_id = node.get('id', '')
            
            # Extrair cores do CSS inline
            css = node.get('css', {})
            if css:
                # Extrair backgroundColor
                if 'backgroundColor' in css:
                    bg_color = css['backgroundColor']
                    if bg_color and bg_color != 'transparent':
                        color_info = self._parse_css_color(bg_color)
                        if color_info:
                            color_key = f"{prefix}bg" if prefix else "bg"
                            colors[color_key] = color_info
                
                # Extrair color (text color)
                if 'color' in css:
                    text_color = css['color']
                    if text_color and text_color != 'transparent':
                        color_info = self._parse_css_color(text_color)
                        if color_info:
                            color_key = f"{prefix}text" if prefix else "text"
                            colors[color_key] = color_info
            
            # Extrair cor de preenchimento
            fills = node.get('fills', [])
            if fills and isinstance(fills, list):
                for i, fill in enumerate(fills):
                    if fill.get('type') == 'SOLID' and fill.get('visible', True):
                        color_data = fill.get('color', {})
                        if color_data:
                            color_info = self.parse_figma_color(color_data)
                            if color_info:
                                color_key = f"{prefix}fill_{i}" if prefix else f"fill_{i}"
                                colors[color_key] = color_info
            
            # Extrair cor de borda
            strokes = node.get('strokes', [])
            if strokes and isinstance(strokes, list):
                for i, stroke in enumerate(strokes):
                    if stroke.get('visible', True):
                        color_data = stroke.get('color', {})
                        if color_data:
                            color_info = self.parse_figma_color(color_data)
                            if color_info:
                                color_key = f"{prefix}stroke_{i}" if prefix else f"stroke_{i}"
                                colors[color_key] = color_info
            
            # Processar filhos recursivamente
            children = node.get('children', [])
            for child in children:
                child_prefix = f"{prefix}{node_id}_" if prefix else f"{node_id}_"
                extract_colors_recursive(child, child_prefix)
        
        # Se figma_data tem estrutura de CSS extraída pelo FigmaReader
        if 'css' in figma_data and isinstance(figma_data['css'], dict):
            # Processar estrutura de CSS extraída
            for node_id, node_data in figma_data['css'].items():
                css = node_data.get('css', {})
                if css:
                    # Extrair backgroundColor
                    if 'backgroundColor' in css:
                        bg_color = css['backgroundColor']
                        if bg_color and bg_color != 'transparent':
                            color_info = self._parse_css_color(bg_color)
                            if color_info:
                                color_key = f"{node_id}_bg"
                                colors[color_key] = color_info
                    
                    # Extrair color (text color)
                    if 'color' in css:
                        text_color = css['color']
                        if text_color and text_color != 'transparent':
                            color_info = self._parse_css_color(text_color)
                            if color_info:
                                color_key = f"{node_id}_text"
                                colors[color_key] = color_info
        else:
            # Processar estrutura normal do Figma
            extract_colors_recursive(figma_data)
        
        return colors
    
    def _parse_css_color(self, css_color: str) -> Optional[ColorInfo]:
        """Converte cor CSS para ColorInfo."""
        try:
            import re
            
            # Padrão para rgba(r, g, b, a)
            rgba_match = re.match(r'rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)', css_color)
            if rgba_match:
                r = int(rgba_match.group(1))
                g = int(rgba_match.group(2))
                b = int(rgba_match.group(3))
                a = float(rgba_match.group(4)) if rgba_match.group(4) else 1.0
                return ColorInfo(r=r, g=g, b=b, a=a)
            
            # Padrão para #hex
            hex_match = re.match(r'#([0-9a-fA-F]{6})', css_color)
            if hex_match:
                hex_color = hex_match.group(1)
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return ColorInfo(r=r, g=g, b=b, a=1.0)
            
            return None
            
        except Exception as e:
            logger.warning(f"Erro ao processar cor CSS '{css_color}': {e}")
            return None
    
    def load_design_system_colors(self, design_system_data: Dict[str, Any]):
        """
        Carrega cores do Design System a partir dos dados extraídos.
        
        Args:
            design_system_data: Dados do Design System com informações de cores
        """
        self.design_system_colors = []
        
        # Extrair cores dos exemplos
        examples = design_system_data.get('examples', {})
        
        for category, category_examples in examples.items():
            if 'colors' in category.lower() or 'cores' in category.lower():
                for example in category_examples:
                    if 'hex' in example and 'class' in example:
                        ds_color = DesignSystemColor(
                            name=example.get('name', ''),
                            class_name=example.get('class', ''),
                            hex=example.get('hex', ''),
                            rgb=example.get('rgb', ''),
                            category=category
                        )
                        self.design_system_colors.append(ds_color)
        
        self._build_color_index()
        logger.info(f"🎨 Carregadas {len(self.design_system_colors)} cores do Design System")


def create_color_mapper_from_design_system(design_system_data: Dict[str, Any]) -> ColorMapper:
    """
    Cria um ColorMapper a partir dos dados do Design System.
    
    Args:
        design_system_data: Dados do Design System
        
    Returns:
        ColorMapper configurado
    """
    mapper = ColorMapper()
    mapper.load_design_system_colors(design_system_data)
    return mapper 